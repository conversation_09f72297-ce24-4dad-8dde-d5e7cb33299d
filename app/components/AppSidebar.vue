<script setup lang="ts">
interface NavigationItem {
    label: string;
    icon: string;
    to?: string;
    children?: NavigationItem[];
}

const navigationItems = ref<NavigationItem[]>([
    {
        label: 'Home',
        icon: 'i-lucide-house',
        to: '/',
    },
    {
        label: 'Merge Block',
        icon: 'i-lucide-git-pull-request-closed',
        to: '/merge-block',
        children: [
            {
                label: 'Requests',
                icon: 'i-lucide-git-pull-request',
                to: '/merge-block/requests',
            },
            {
                label: 'History',
                icon: 'i-lucide-history',
                to: '/merge-block/history',
            },
            {
                label: 'Settings',
                icon: 'i-lucide-cog',
                to: '/merge-block/settings',
            },
        ],
    },
    {
        label: 'Users',
        icon: 'i-lucide-users',
        to: '/users',
    },
    {
        label: 'Audit Logs',
        icon: 'i-lucide-logs',
        to: '/audit-logs',
    },
]);

const route = useRoute();
const router = useRouter();

const isDesktop = ref(false);
const isSidebarOpen = ref(false);
const expandedItems = ref<Set<string>>(new Set());

// Check if an item is active
const isItemActive = (item: NavigationItem): boolean => {
    if (item.to === route.path) return true;
    if (item.children) {
        return item.children.some(child => child.to === route.path);
    }
    return false;
};

// Toggle expanded state for items with children
const toggleExpanded = (itemLabel: string) => {
    if (expandedItems.value.has(itemLabel)) {
        expandedItems.value.delete(itemLabel);
    }
    else {
        expandedItems.value.add(itemLabel);
    }
};

// Initialize expanded state based on current route
const initializeExpandedState = () => {
    navigationItems.value.forEach((item) => {
        if (item.children && isItemActive(item)) {
            expandedItems.value.add(item.label);
        }
    });
};

const initializeSidebar = () => {
    if (!import.meta.client) return;

    const mediaQuery = window.matchMedia('(min-width: 1024px)'); // lg breakpoint
    isDesktop.value = mediaQuery.matches;
    isSidebarOpen.value = isDesktop.value;

    const handleScreenChange = (e: MediaQueryListEvent) => {
        isDesktop.value = e.matches;
        isSidebarOpen.value = isDesktop.value;
    };

    mediaQuery.addEventListener('change', handleScreenChange);

    return () => {
        mediaQuery.removeEventListener('change', handleScreenChange);
    };
};

const closeSidebarOnNavigation = () => {
    if (!isDesktop.value) {
        isSidebarOpen.value = false;
    }
};

const toggleSidebar = () => {
    isSidebarOpen.value = !isSidebarOpen.value;
};

const handleItemClick = (item: NavigationItem) => {
    if (item.children) {
        toggleExpanded(item.label);
        // If the item is now expanded and has children, navigate to the first child (only if one if its children is not already selected)
        if (expandedItems.value.has(item.label) && item?.children?.[0] && !item.children.some(child => child.to === route.path)) {
            handleChildClick(item.children[0]);
        }
    }
    else if (item.to) {
        router.push(item.to);
    }
};

const handleChildClick = (child: NavigationItem) => {
    if (child.to) {
        router.push(child.to);
    }
};

onMounted(() => {
    initializeSidebar();
    initializeExpandedState();
    router.afterEach(closeSidebarOnNavigation);
});

// Watch route changes to update expanded state
watch(() => route.path, () => {
    initializeExpandedState();
});

defineExpose({
    toggleSidebar,
    isSidebarOpen: readonly(isSidebarOpen),
    isDesktop: readonly(isDesktop),
});
</script>

<template>
    <div>
        <div
            :class="[
                'transition-all duration-300 ease-in-out bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800',
                isDesktop ? 'w-64' : (isSidebarOpen ? 'w-64' : 'w-0'),
                'lg:relative absolute z-10 h-full',
            ]"
        >
            <div
                v-if="isDesktop || isSidebarOpen"
                class="flex flex-col h-[calc(100vh-4rem)] py-2 px-2"
            >
                <nav class="flex-1 space-y-1">
                    <div
                        v-for="item in navigationItems"
                        :key="item.label"
                        class="space-y-1"
                    >
                        <!-- Main navigation item -->
                        <div
                            :class="[
                                'group flex items-center justify-between p-3 text-sm font-medium rounded-md cursor-pointer transition-colors duration-200',
                                isItemActive(item)
                                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300'
                                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800',
                            ]"
                            @click="handleItemClick(item)"
                        >
                            <div class="flex items-center">
                                <UIcon
                                    :name="item.icon"
                                    :class="[
                                        'mr-3 h-5 w-5 flex-shrink-0',
                                        isItemActive(item)
                                            ? 'text-blue-600 dark:text-blue-400'
                                            : 'text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300',
                                    ]"
                                />
                                <span>{{ item.label }}</span>
                            </div>

                            <!-- Chevron for expandable items -->
                            <UIcon
                                v-if="item.children"
                                :name="expandedItems.has(item.label) ? 'i-lucide-chevron-up' : 'i-lucide-chevron-down'"
                                :class="[
                                    'h-4 w-4 transition-transform duration-200',
                                    isItemActive(item)
                                        ? 'text-blue-600 dark:text-blue-400'
                                        : 'text-gray-400 dark:text-gray-500',
                                ]"
                            />
                        </div>

                        <!-- Sub-navigation items -->
                        <div
                            v-if="item.children && expandedItems.has(item.label)"
                            class="ml-8 space-y-1"
                        >
                            <div
                                v-for="child in item.children"
                                :key="child.label"
                                :class="[
                                    'group flex items-center p-3 text-sm font-medium rounded-md cursor-pointer transition-colors duration-200',
                                    child.to === route.path
                                        ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
                                        : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800/50 hover:text-gray-900 dark:hover:text-gray-200',
                                ]"
                                @click="handleChildClick(child)"
                            >
                                <UIcon
                                    :name="child.icon"
                                    :class="[
                                        'mr-3 h-5 w-5 flex-shrink-0',
                                        child.to === route.path
                                            ? 'text-blue-600 dark:text-blue-400'
                                            : 'text-gray-400 dark:text-gray-500 group-hover:text-gray-600 dark:group-hover:text-gray-400',
                                    ]"
                                />
                                <span>{{ child.label }}</span>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </div>

        <div
            v-if="isSidebarOpen && !isDesktop"
            class="fixed inset-0 bg-[rgba(0,0,0,0.5)] z-0 lg:hidden"
            @click="isSidebarOpen = false"
        />
    </div>
</template>
