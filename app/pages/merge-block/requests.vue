<script setup lang="ts">
</script>

<template>
    <div class="p-6">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                Introduction
            </h1>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                        Getting Started
                    </h2>
                </div>
                <div class="p-6">
                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                        Welcome to the Introduction section. This page provides an overview of the merge block functionality and how to get started.
                    </p>
                    <div class="prose dark:prose-invert max-w-none">
                        <h3>What is Merge Block?</h3>
                        <p>
                            Merge Block is a powerful feature that allows you to manage and organize your project components efficiently.
                            It provides a comprehensive set of tools for handling complex project structures.
                        </p>

                        <h3>Key Features</h3>
                        <ul>
                            <li>Intuitive project management interface</li>
                            <li>Real-time collaboration tools</li>
                            <li>Advanced filtering and search capabilities</li>
                            <li>Customizable workflows</li>
                        </ul>

                        <h3>Getting Started</h3>
                        <p>
                            To begin using Merge Block, navigate through the different sections using the sidebar menu.
                            Each section provides detailed information and tools for specific aspects of project management.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
