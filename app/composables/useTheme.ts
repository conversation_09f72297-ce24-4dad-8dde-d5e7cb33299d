export type Theme = 'light' | 'dark' | 'system';

export const useTheme = () => {
    const theme = ref<Theme>('system');
    const isDark = ref(false);

    const isClient = import.meta.client;

    const initializeTheme = () => {
        if (!isClient) return;

        const savedTheme = localStorage.getItem('theme') as Theme | null;
        theme.value = savedTheme || 'system';
        updateTheme();
    };

    const updateTheme = () => {
        if (!isClient) return;

        const root = document.documentElement;

        if (theme.value === 'system') {
            // Remove manual theme classes and let system preference take over
            root.classList.remove('dark');
            isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches;
        }
        else if (theme.value === 'dark') {
            root.classList.add('dark');
            isDark.value = true;
        }
        else {
            root.classList.remove('dark');
            isDark.value = false;
        }
    };

    const setTheme = (newTheme: Theme) => {
        theme.value = newTheme;

        if (!isClient) return;

        if (newTheme === 'system') {
            localStorage.removeItem('theme');
        }
        else {
            localStorage.setItem('theme', newTheme);
        }

        updateTheme();
    };

    const setupSystemThemeListener = () => {
        if (!isClient) return;

        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

        const handleSystemThemeChange = () => {
            if (theme.value === 'system') {
                isDark.value = mediaQuery.matches;
            }
        };

        mediaQuery.addEventListener('change', handleSystemThemeChange);

        return () => {
            mediaQuery.removeEventListener('change', handleSystemThemeChange);
        };
    };

    onMounted(() => {
        initializeTheme();
        setupSystemThemeListener();
    });

    return {
        theme: readonly(theme),
        isDark: readonly(isDark),
        setTheme,
        initializeTheme,
    };
};
